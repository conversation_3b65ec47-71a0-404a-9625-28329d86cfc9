body{
    /* 增加锚点跳转过度效果 */
    scroll-behavior: smooth;
}
/* 锚点元素位置 */
.anchor-element {
    position: absolute;
    /*控制距离顶部 100px*/
    top: -180px;
    visibility: hidden;
}
.link-line {
    font-size: 16px;
    /*border-bottom: 1px solid #dddddd;*/
    height:120px;
    position:relative;
    overflow: hidden;
}
.link-line .angle{
    width:50px;
    height:70px;
    position: absolute;
    background: #FF5722;
    top:-50px;
    right:-50px;
    transform: rotate(45deg);
    color:#fff;
}
.link-line .angle span{
    position: absolute;
    bottom:0;
    display: block;
    width:20px;
    text-align: center;
}
.link-line img {
    width: 16px;
    height: 16px;
    /*border-radius: 50%;*/
    margin-right: 1px;
    position: relative;
    top:3px;
}
.link-line a{
    color:#212121;
    text-decoration:none;
}
.link-space{
    margin-top:16px;
    z-index:999;
}
.link-title{
    /*color:rgb(0,0,0);*/
    font-size:14px;
    font-weight:bold;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height:15px;
}
.link-content{
    text-overflow: ellipsis;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical; 
    -webkit-line-clamp: 2; 
    
}
.category-name{
    padding-left:8px;
}
.category-name i {
    padding-right:12px;
}
footer{
    width:100%;
    /* margin-top:2em; */
    text-align:center;
    line-height: 18px;
    margin-top:2em;
    margin-bottom: 2em;
}
footer a{
    color:#8C9EFF;
    text-decoration: none;
}

.top{
	position:fixed;
	right:10px;
	bottom:30px;
    z-index:1000;
    border-radius: 40px;
    /* border:1px solid red; */
}
.top i{
    font-size: 40px; color: #1E9FFF;
    font-weight: bold;
}
.hidden {
    display: none;
}
/* 显示二维码 */
.show_qrcode{
    height: 300px;
    width: 260px;
    text-align: center;
}
.show_qrcode img{
    text-align: center;
    max-height: 200px;
    max-width: 200px;
}

/* msg弹窗提醒 */
.msg{
    width:40%;
    line-height: 16px;
    text-align: center;
    padding:16px;
    position: fixed;
    top:30%;
    left:30%;
    /* color:#FFFFFF; */
    /* border: 1px #009933 solid; */
    background-color:rgba(0, 0, 0, 0.3);
    /* box-shadow:2px 2px 2px #aaa; */
    border-radius: 2px;
    display: none;
}
.mdui-dialog-title{
    overflow:hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    -o-text-overflow:ellipsis;
}
.mdui-snackbar-top{
    background-color: #FF2377;
}
.cat-title{
    margin-top: -50px;
    padding-top:68px;
}

/* :target {
    padding-top: 70px;
    margin-top: -70px;
  } */
  
.category_sub{
    font-size:14px;
    margin-left:0px;
    /*padding-left:8px;*/
}
.category_sub i{
    padding-right:5px;
}
#drawer{
    /* background-color: #FEFEFE; */
    /* box-shadow:1px 0 5px 0 #eeeeee; */
    border-right: 0.5px solid rgba(0, 0, 0, 0.12);
}