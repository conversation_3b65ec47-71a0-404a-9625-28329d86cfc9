@charset "utf-8";
html{height: 100%;}
h1,h2,h3,h4,h5{
    color: black;
}

li {
  line-height: 2.2;
}
/*图片边距*/
img {
  margin: 10px;
}
/*代码段样式*/
pre {
  background-color: #f2f2f2;
  border: 1px solid #ccc;
  border-radius: 5px;
  padding: 10px;
  margin: 10px 0;
  font-family: "Courier New", monospace;
  font-size: 14px;
  line-height: 1.5;
  overflow-x: auto; 
  box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.1);
}
/*表格样式*/
.mdui-table {
  border-collapse: collapse;
  width: 100%;
}
.mdui-table th,
.mdui-table td {
  border: 1px solid #ccc;
  padding: 8px;
  text-align: center;
}
.mdui-table th::after,
.mdui-table td::after {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  right: -1px;
  width: 1px;
  background-color: #ccc; 
}

/*a标签样式*/
.container a {
  text-decoration: none;
  color: inherit;
  font-family: Arial, sans-serif;
  color: #007bff;
  padding: 5px 10px;
  border-radius: 5px;
  background-color: #f2f2f2;
}
.container a:hover {
  color: #ff4500;
}

/*文章标题样式*/
.mdui-typo-title, .mdui-typo-title-opacity {
    font-size: 1em; 
}
/*prism 代码上色*/
code[class*=language-],pre[class*=language-]{color:#000;background:0 0;text-shadow:0 1px #fff;font-family:Consolas,Monaco,'Andale Mono','Ubuntu Mono',monospace;font-size:1em;text-align:left;white-space:pre;word-spacing:normal;word-break:normal;word-wrap:normal;line-height:1.5;-moz-tab-size:4;-o-tab-size:4;tab-size:4;-webkit-hyphens:none;-moz-hyphens:none;-ms-hyphens:none;hyphens:none}code[class*=language-] ::-moz-selection,code[class*=language-]::-moz-selection,pre[class*=language-] ::-moz-selection,pre[class*=language-]::-moz-selection{text-shadow:none;background:#b3d4fc}code[class*=language-] ::selection,code[class*=language-]::selection,pre[class*=language-] ::selection,pre[class*=language-]::selection{text-shadow:none;background:#b3d4fc}@media print{code[class*=language-],pre[class*=language-]{text-shadow:none}}pre[class*=language-]{padding:1em;margin:.5em 0;overflow:auto}:not(pre)>code[class*=language-],pre[class*=language-]{background:#f5f2f0}:not(pre)>code[class*=language-]{padding:.1em;border-radius:.3em;white-space:normal}.token.cdata,.token.comment,.token.doctype,.token.prolog{color:#708090}.token.punctuation{color:#999}.token.namespace{opacity:.7}.token.boolean,.token.constant,.token.deleted,.token.number,.token.property,.token.symbol,.token.tag{color:#905}.token.attr-name,.token.builtin,.token.char,.token.inserted,.token.selector,.token.string{color:#690}.language-css .token.string,.style .token.string,.token.entity,.token.operator,.token.url{color:#9a6e3a;background:hsla(0,0%,100%,.5)}.token.atrule,.token.attr-value,.token.keyword{color:#07a}.token.class-name,.token.function{color:#dd4a68}.token.important,.token.regex,.token.variable{color:#e90}.token.bold,.token.important{font-weight:700}.token.italic{font-style:italic}.token.entity{cursor:help}