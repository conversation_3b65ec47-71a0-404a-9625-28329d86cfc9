---
type: "always_apply"
---

默认上下文： 除非明确指示，所有操作默认在 context7 环境下执行。

调用 MCP sequentialthinking 进行深度思考。

严格按照设计和全局开发规范，高效、精确地完成编码工作。

完成编码后，对代码进行严格的测试，确保其功能、性能和稳定性符合要求。优先使用 playwright 工具进行自动化测试和问题溯源。

交互协议与工作流 (Interaction Protocol & Workflow)
这是你与用户交互的铁律。

全局开发规范

在工作中必须遵守以下规范：

代码水印: 所有代码文件的顶部必须加入版权水印。格式为：
# Copyright (c) 2025 Aoki. All rights reserved.

配置中心: 所有变量由统一的配置文件管理，禁止重复定义。

在开发和测试过程中，禁止简化代码和简化api和模拟数据来达到测试目的。具体要求：

1. **禁止使用简化版**：不得使用或其他简化版本进行功能测试，必须使用完整版本

2. **禁止简化API实现**：所有API端点必须实现完整的业务逻辑，包括：
   - 完整的数据验证
   - 正确的错误处理
   - 完整的数据库操作
   - 适当的安全检查

3. **禁止跳过依赖解决**：遇到依赖问题时，必须正确安装和配置所有必需的依赖包，而不是通过简化代码来绕过问题

4. **禁止模拟数据替代**：测试时必须使用真实的数据源和服务，不得用硬编码的模拟数据替代实际的数据库查询或外部服务调用

5. **要求完整测试流程**：所有功能测试必须在完整的生产环境配置下进行，确保测试结果能够真实反映生产环境的表现

这一规则旨在确保开发和测试的质量，避免因使用简化版本而掩盖潜在问题，保证最终交付的系统具有生产级别的稳定性和可靠性。


 项目开发规范

 任务管理
- 使用 `Aotd.md` 文件跟踪所有开发任务
- 每个任务都要有明确的描述和完成标准
- 任务状态：[ ] 未开始、[/] 进行中、[x] 已完成、[-] 已取消

 开发流程
1. **开发前**: 更新Aotd.md中的任务状态
2. **开发中**: 遵循现代化技术栈规范（Vue3+FastAPI）
3. **开发后**: 进行功能测试和验证
4. **完成后**: 更新任务状态，记录到log.md

文档维护
- 保持README.md、PROJECT_ARCHITECTURE.md等核心文档的更新
- 汇报详细日志记录到log.md日志中
- 确保文档与代码实际状态保持一致

质量保证
- 每次修改后都要进行功能测试
- 确保前后端服务正常启动和运行
- 验证用户界面和管理后台功能正常
- 清理临时文件和测试文件

文件组织
- 按照项目架构文档组织文件结构
- 新文件放到对应的模块目录中
- 删除不再需要的旧文件和冗余代码

 项目代码质量和安全规范

 安全开发规范

 代码安全
- 禁止在代码中硬编码敏感信息（密码、API密钥、数据库连接字符串）
- 所有敏感配置必须使用环境变量或配置文件
- 生产环境配置文件不得提交到版本控制
- 定期检查和更新依赖包的安全漏洞

认证和授权
- 所有API接口必须进行适当的认证和授权检查
- 使用强密码策略和密码哈希存储
- 实施会话管理和令牌过期机制
- 记录所有认证和授权相关的操作日志

网络安全
- 正确配置CORS策略，避免过于宽松的设置
- 实施API限流和防止暴力攻击
- 使用HTTPS进行数据传输
- 验证和清理所有用户输入，防止注入攻击

代码质量规范

 代码风格
- 遵循项目统一的代码风格和命名规范
- 使用有意义的变量名和函数名
- 保持代码简洁、可读性强
- 添加必要的注释和文档字符串

测试要求
- 新功能必须编写对应的单元测试
- 关键业务逻辑需要集成测试覆盖
- 测试覆盖率不低于80%
- 所有测试必须通过才能合并代码

 代码审查
- 所有代码变更必须经过代码审查
- 审查重点关注安全性、性能和可维护性
- 修复所有静态代码分析工具发现的问题
- 确保代码符合项目架构设计

 性能优化规范

 性能要求
- API响应时间不超过2秒
- 数据库查询必须使用索引优化
- 实施适当的缓存策略
- 避免N+1查询问题

 监控和日志
- 关键操作必须记录详细日志
- 实施性能监控和告警机制
- 定期分析系统性能指标
- 建立错误追踪和报告机制

 部署和运维规范

 容器化要求
- 所有服务必须支持Docker容器化部署
- 使用多阶段构建优化镜像大小
- 正确配置健康检查和资源限制
- 实施容器安全扫描

 CI/CD流程
- 代码提交触发自动化测试
- 通过所有测试后才能部署
- 实施蓝绿部署或滚动更新
- 保持部署回滚能力

文档维护
- 及时更新API文档和技术文档
- 记录重要的架构决策和变更
- 维护部署和运维手册
- 建立故障排除指南

合规性要求

许可证合规
- 检查所有第三方依赖的许可证
- 确保许可证兼容性
- 记录和维护许可证清单
- 避免使用有争议的开源许可证

 数据保护
- 遵循数据保护法规要求
- 实施数据加密和访问控制
- 建立数据备份和恢复机制
- 定期进行安全审计

 违规处理

严重违规
- 硬编码敏感信息 → 立即修复，安全审查
- 未经授权的数据访问 → 立即回滚，调查原因
- 生产环境直接修改 → 立即恢复，流程整改

一般违规
- 代码风格不符 → 要求修改后重新提交
- 测试覆盖率不足 → 补充测试用例
- 文档缺失 → 补充相关文档

工具和检查

 必需工具
- 代码格式化工具（Black、Prettier等）
- 静态代码分析工具（ESLint、Pylint等）
- 安全扫描工具（Bandit、npm audit等）
- 依赖漏洞检查工具

 自动检查
- 提交前自动格式化代码
- CI流程中运行所有检查
- 定期扫描安全漏洞
- 监控代码质量指标

---

注意**: 本规范为强制性要求，所有团队成员必须严格遵守。违反规范的代码将被拒绝合并。